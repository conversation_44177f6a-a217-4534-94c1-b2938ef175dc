<?php

/**
 * Debug script to test cart route accessibility
 * Run this with: php debug_cart_route.php
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

echo "=== Cart Route Debug ===\n\n";

// Test 1: Check if route exists
echo "1. Checking route registration:\n";
try {
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    $cartRoute = $routes->getByName('cart.store');
    
    if ($cartRoute) {
        echo "   ✓ cart.store route exists\n";
        echo "   URI: " . $cartRoute->uri() . "\n";
        echo "   Methods: " . implode(', ', $cartRoute->methods()) . "\n";
        echo "   Action: " . $cartRoute->getActionName() . "\n";
        
        // Check middleware
        $middleware = $cartRoute->middleware();
        if (empty($middleware)) {
            echo "   ✓ No middleware applied\n";
        } else {
            echo "   ⚠ Middleware applied: " . implode(', ', $middleware) . "\n";
        }
    } else {
        echo "   ✗ cart.store route NOT FOUND\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error checking routes: " . $e->getMessage() . "\n";
}

// Test 2: Check controller method
echo "\n2. Checking controller method:\n";
try {
    if (method_exists(\App\Http\Controllers\CartCont::class, 'add_cart')) {
        echo "   ✓ CartCont::add_cart method exists\n";
        
        $reflection = new ReflectionMethod(\App\Http\Controllers\CartCont::class, 'add_cart');
        if ($reflection->isPublic()) {
            echo "   ✓ Method is public\n";
        } else {
            echo "   ✗ Method is not public\n";
        }
    } else {
        echo "   ✗ CartCont::add_cart method NOT FOUND\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error checking controller: " . $e->getMessage() . "\n";
}

// Test 3: Check for route conflicts
echo "\n3. Checking for route conflicts:\n";
try {
    $allRoutes = \Illuminate\Support\Facades\Route::getRoutes();
    $cartRoutes = [];
    
    foreach ($allRoutes as $route) {
        if (strpos($route->uri(), 'cart') !== false) {
            $cartRoutes[] = [
                'uri' => $route->uri(),
                'methods' => $route->methods(),
                'name' => $route->getName(),
                'action' => $route->getActionName(),
                'middleware' => $route->middleware()
            ];
        }
    }
    
    echo "   Found " . count($cartRoutes) . " cart-related routes:\n";
    foreach ($cartRoutes as $route) {
        echo "   - " . implode('|', $route['methods']) . " " . $route['uri'] . " -> " . $route['action'];
        if (!empty($route['middleware'])) {
            echo " [Middleware: " . implode(', ', $route['middleware']) . "]";
        }
        echo "\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error checking route conflicts: " . $e->getMessage() . "\n";
}

// Test 4: Check session configuration
echo "\n4. Checking session configuration:\n";
try {
    $sessionDriver = config('session.driver');
    $sessionLifetime = config('session.lifetime');
    
    echo "   Session driver: $sessionDriver\n";
    echo "   Session lifetime: $sessionLifetime minutes\n";
    
    if ($sessionDriver === 'file') {
        $sessionPath = config('session.files');
        echo "   Session path: $sessionPath\n";
        if (is_writable($sessionPath)) {
            echo "   ✓ Session path is writable\n";
        } else {
            echo "   ✗ Session path is not writable\n";
        }
    }
} catch (Exception $e) {
    echo "   ✗ Error checking session config: " . $e->getMessage() . "\n";
}

echo "\n=== Recommendations ===\n";
echo "1. Clear route cache: php artisan route:clear\n";
echo "2. Clear config cache: php artisan config:clear\n";
echo "3. Check browser network tab for actual request/response\n";
echo "4. Check Laravel logs: tail -f storage/logs/laravel.log\n";
echo "5. Test with curl: curl -X POST http://yoursite.com/cart -d 'product_id=1&prod_color=1&prod_size=1&quantity=1' -H 'Content-Type: application/x-www-form-urlencoded'\n";

?>
