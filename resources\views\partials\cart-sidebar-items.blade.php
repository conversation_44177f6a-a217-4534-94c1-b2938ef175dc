{{-- Cart Sidebar Items Partial --}}
<div class="cart_select_items py-2" style="max-height: 70vh; overflow-y:scroll">
    @foreach ($cart_items as $cart)
        <div class="d-flex align-items-center justify-content-between px-3 py-3">
            <div class="cart_single d-flex align-items-center">
                <div class="cart_selected_single_thumb" style="border: 1px solid rgba(0,0,0,.125)">
                    <a href="{{route('product.details', $cart->relto_product->slug)}}">
                        <img src="{{asset('uploads/product/preview/'.$cart->relto_product->preview)}}" width="60" class="img-fluid" alt="" />
                    </a>
                </div>
                <div class="cart_single_caption pl-2">
                    <h4 class="product_title fs-sm ft-medium mb-0 lh-1">{{$cart->relto_product->product_name}}</h4>
                    <p class="mb-2">
                        <span class="text-dark ft-medium small">{{$cart->relto_color->color_name}}</span>, 
                        <span class="text-dark small">{{$cart->relto_size->size}}</span>
                    </p>
                    <h4 class="fs-sm ft-medium mb-0 lh-1">
                        EUR {{number_format($cart->relto_product->after_disc)}}
                        * {{$cart->quantity}}
                    </h4>
                </div>
            </div>

            @if (!request()->route()->named('checkout'))
                <div class="fls_last">
                    @if(Auth::guard('cust_login')->check())
                        <a href="{{route('cart.remove', $cart->id)}}" class="close_slide gray"><i class="ti-close"></i></a>
                    @else
                        <a href="#" onclick="removeGuestCartItem('{{$cart->id}}')" class="close_slide gray"><i class="ti-close"></i></a>
                    @endif
                </div>
            @endif
        </div>
    @endforeach
</div>

<div class="d-flex align-items-center justify-content-between br-top br-bottom px-3 py-3">
    <h6 class="mb-0">
        @if (session('lang_ben'))
            সামগ্রীর সমষ্টি
        @elseif (session('lang_fin'))
            Ostoskorin sisältö
        @else
            Subtotal
        @endif
    </h6>
    <h3 class="mb-0 ft-medium">{{number_format($total)}} &#8364;</h3>
</div>

<div class="cart_action px-3 py-3">
    <div class="form-group">
        @if(Auth::guard('cust_login')->check())
            <a href="{{route('cart.remove_all')}}" style="width: 45%" class="btn btn-dark-light">
                @if (session('lang_ben'))
                সব সামগ্রী সরান
                @elseif (session('lang_fin'))
                    Poista kaikki
                @else
                    Remove All
                @endif
            </a>
            <a href="{{route('cart.store.update')}}" style="width: 45%" class="btn btn-dark-light">
                @if (session('lang_ben'))
                    কার্ট দেখুন
                @elseif (session('lang_fin'))
                    Katso kori
                @else
                    View Cart
                @endif
            </a>
        @else
            <a href="#" onclick="clearGuestCart()" style="width: 45%" class="btn btn-dark-light">
                @if (session('lang_ben'))
                সব সামগ্রী সরান
                @elseif (session('lang_fin'))
                    Poista kaikki
                @else
                    Remove All
                @endif
            </a>
            <a href="{{route('checkout')}}" style="width: 45%" class="btn btn-dark-light">
                @if (session('lang_ben'))
                    চেকআউট
                @elseif (session('lang_fin'))
                    Kassalle
                @else
                    Checkout
                @endif
            </a>
        @endif
    </div>
</div>
