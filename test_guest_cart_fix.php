<?php

/**
 * Test script to verify guest cart fix
 * Run this with: php test_guest_cart_fix.php
 */

echo "=== Guest Cart Fix Verification ===\n\n";

// Test 1: Check if cart routes are outside middleware
echo "1. Checking routes configuration:\n";
$routesContent = file_get_contents('routes/web.php');

// Check if cart routes are in public section
if (strpos($routesContent, '// === Cart Routes (Public') !== false) {
    echo "   ✓ Cart routes moved to public section\n";
} else {
    echo "   ✗ Cart routes not found in public section\n";
}

// Check if cart routes are no longer in middleware group
$middlewareSection = substr($routesContent, strpos($routesContent, "Route::middleware('cust_login')"), strpos($routesContent, '});') - strpos($routesContent, "Route::middleware('cust_login')"));

if (strpos($middlewareSection, 'cart.store') === false) {
    echo "   ✓ Cart routes removed from authentication middleware\n";
} else {
    echo "   ✗ Cart routes still in authentication middleware\n";
}

// Test 2: Check CartCont controller fixes
echo "\n2. Checking CartCont controller:\n";
$cartContContent = file_get_contents('app/Http/Controllers/CartCont.php');

if (strpos($cartContContent, 'public function add_cart') !== false) {
    echo "   ✓ add_cart method has public visibility\n";
} else {
    echo "   ✗ add_cart method missing public visibility\n";
}

if (strpos($cartContContent, "'id' => uniqid()") !== false) {
    echo "   ✓ Guest cart items have unique IDs\n";
} else {
    echo "   ✗ Guest cart items missing unique IDs\n";
}

if (strpos($cartContContent, 'Product_list::find') !== false) {
    echo "   ✓ Using correct Product_list model\n";
} else {
    echo "   ✗ Not using correct Product_list model\n";
}

// Test 3: Check guest cart logic
echo "\n3. Checking guest cart logic:\n";

// Check if guest cart has proper structure
if (strpos($cartContContent, "session()->put('guest_cart', \$cart)") !== false) {
    echo "   ✓ Guest cart session storage implemented\n";
} else {
    echo "   ✗ Guest cart session storage missing\n";
}

// Check if AJAX response is properly handled
if (strpos($cartContContent, 'count(\$cart)') !== false) {
    echo "   ✓ Guest cart count calculation implemented\n";
} else {
    echo "   ✗ Guest cart count calculation missing\n";
}

// Test 4: Check for common issues
echo "\n4. Checking for potential issues:\n";

// Check if there are any authentication checks that might block guests
if (strpos($cartContContent, 'Auth::guard') !== false && strpos($cartContContent, 'else {') !== false) {
    echo "   ✓ Authentication check with guest fallback exists\n";
} else {
    echo "   ✗ Authentication check or guest fallback missing\n";
}

// Check if validation is present
if (strpos($cartContContent, '$request->validate') !== false) {
    echo "   ✓ Input validation implemented\n";
} else {
    echo "   ✗ Input validation missing\n";
}

echo "\n=== Fix Summary ===\n";
echo "The main issue was that cart routes were inside the 'cust_login' middleware group,\n";
echo "which prevented guest users from accessing the add_cart functionality.\n\n";

echo "Changes made:\n";
echo "1. ✅ Moved cart routes outside authentication middleware\n";
echo "2. ✅ Added public visibility to controller methods\n";
echo "3. ✅ Fixed guest cart item structure with unique IDs\n";
echo "4. ✅ Fixed Product model reference to Product_list\n";
echo "5. ✅ Improved guest cart message handling\n\n";

echo "Next steps:\n";
echo "1. Clear route cache: php artisan route:clear\n";
echo "2. Test guest cart functionality in browser\n";
echo "3. Check browser console for any JavaScript errors\n";
echo "4. Verify AJAX requests are reaching the controller\n\n";

echo "🎉 Guest cart should now work for logged-out users!\n";

?>
